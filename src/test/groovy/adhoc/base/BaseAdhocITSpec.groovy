package adhoc.base

import adhoc.helper.AdhocHelper
import ch.qos.logback.classic.Logger
import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.core.read.ListAppender
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import io.reactivex.Flowable
import io.reactivex.processors.PublishProcessor
import java.math.BigInteger
import java.util.concurrent.CompletableFuture
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger
import org.slf4j.LoggerFactory
import org.web3j.protocol.Web3j
import org.web3j.protocol.core.Request
import org.web3j.protocol.core.methods.request.EthFilter
import org.web3j.protocol.core.methods.response.EthLog
import org.web3j.protocol.websocket.events.NewHeadsNotification
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.*
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.*
import spock.lang.Shared
import spock.lang.Specification

/**
 * Base class for adhoc integration tests that provides common setup and cleanup functionality.
 * This class contains shared methods for:
 * - S3 bucket management (clearing contents)
 * - DynamoDB table management (clearing contents)
 * - Web3j mock setup
 * - Event stream and pending event setup
 */
abstract class BaseAdhocITSpec extends Specification {

	// Common test constants
	static final String TEST_BUCKET = "abijson-local-bucket"
	static final String EVENTS_TABLE = "local-Events"
	static final String BLOCK_HEIGHT_TABLE = "local-BlockHeight"

	// Common shared fields
	@Shared
	DynamoDbClient dynamoDbClient

	@Shared
	S3Client s3Client

	@Shared
	Web3j web3j = Mock(Web3j)

	@Shared
	ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2)

	// Non-shared log appender - each test gets its own instance
	ListAppender<ILoggingEvent> logAppender

	@Shared
	def loggingServiceLogger = LoggerFactory.getLogger(LoggingService.class) as Logger

	/**
	 * Common setupSpec implementation for creating AWS resources
	 */
	def setupSpecCommon() {
		// Initialize AWS clients
		def localStackPort = AdhocHelper.getLocalStackPort()
		def endpoint = "http://localhost:${localStackPort}"

		// Create DynamoDB client for LocalStack
		dynamoDbClient = DynamoDbClient.builder()
				.endpointOverride(URI.create(endpoint))
				.credentialsProvider(StaticCredentialsProvider.create(
				AwsBasicCredentials.create("access123", "secret123")))
				.region(Region.AP_NORTHEAST_1)
				.build()

		// Create S3 client for LocalStack
		s3Client = S3Client.builder()
				.endpointOverride(URI.create(endpoint))
				.credentialsProvider(StaticCredentialsProvider.create(
				AwsBasicCredentials.create("access123", "secret123")))
				.region(Region.AP_NORTHEAST_1)
				.forcePathStyle(true)
				.build()

		// Create tables and bucket
		AdhocHelper.createEventsTable(dynamoDbClient, EVENTS_TABLE)
		AdhocHelper.createBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		AdhocHelper.createS3Bucket(s3Client, TEST_BUCKET)
	}

	/**
	 * Common cleanupSpec implementation for closing AWS clients
	 */
	def cleanupSpecCommon() {
		dynamoDbClient?.close()
		s3Client?.close()
	}

	/**
	 * Common setup implementation for test preparation
	 */
	def setupCommon() {
		// Clear all S3 bucket contents completely
		clearS3Bucket()
		// Clear all DynamoDB table contents
		clearDynamoDBTables()
		// Setup web3j mock
		setupWeb3jMock()
		// Create a new log appender for this test to avoid log pollution
		logAppender = new ListAppender<>()
		logAppender.start()
		loggingServiceLogger.addAppender(logAppender)
		AdhocHelper.resetRunningFlag()
	}

	/**
	 * Common cleanup implementation for test cleanup
	 */
	def cleanupCommon() {
		// Remove log appender to prevent log pollution between tests
		if (logAppender != null) {
			// Detach from all loggers that might be using this appender
			loggingServiceLogger.detachAppender(logAppender)
			logAppender.stop()
			logAppender = null
		}
		// Clear S3 bucket for next test
		clearS3Bucket()
		// Shut down the scheduler to stop mock event generation
		scheduler.shutdown()
		scheduler.awaitTermination(5, TimeUnit.SECONDS)
		// Recreate scheduler for next test
		scheduler = Executors.newScheduledThreadPool(2)
	}

	/**
	 * Setup Web3j mock using reflection to inject into Web3jConfig
	 */
	protected void setupWeb3jMock() {
		def field = Web3jConfig.class.getDeclaredField("web3j")
		field.setAccessible(true)
		field.set(getWeb3jConfig(), web3j)

		// Setup default mocking for empty blocks
		setupEmptyBlockMocking()

		println("Web3j mock setup completed")
	}

	/**
	 * Setup mocking for empty blocks (blocks that exist but contain no events)
	 */
	protected void setupEmptyBlockMocking() {
		// Mock ethGetBlockByNumber to return empty blocks
		def mockRequest = Mock(Request)
		def mockEthBlock = Mock(org.web3j.protocol.core.methods.response.EthBlock)
		def mockBlock = Mock(org.web3j.protocol.core.methods.response.EthBlock.Block)

		// Setup empty block with no transactions
		mockBlock.getTransactions() >> Collections.emptyList()
		mockBlock.getNumber() >> BigInteger.valueOf(1000)
		mockBlock.getTimestamp() >> BigInteger.valueOf(System.currentTimeSeconds())

		mockEthBlock.getBlock() >> mockBlock
		mockRequest.sendAsync() >> CompletableFuture.completedFuture(mockEthBlock)

		web3j.ethGetBlockByNumber(_, true) >> mockRequest

		println("Empty block mocking setup completed")
	}

	/**
	 * Abstract method to get Web3jConfig instance - must be implemented by subclasses
	 */
	abstract Web3jConfig getWeb3jConfig()

	/**
	 * Setup event stream for Web3j mock
	 */
	protected void setUpEventStream(List<NewHeadsNotification> blocks) {
		def processor = PublishProcessor.<NewHeadsNotification> create()
		def index = new AtomicInteger(0)
		scheduler.scheduleAtFixedRate({
			int i = index.getAndIncrement()
			if (i < blocks.size()) {
				processor.onNext(blocks.get(i))
			}
		}, 0, 2, TimeUnit.SECONDS)

		web3j.newHeadsNotifications() >> Flowable.fromPublisher(processor)
	}

	/**
	 * Setup pending event for Web3j mock
	 */
	protected void setUpPendingEvent(List<EthLog.LogResult> resultList) {
		def mockRequest = Mock(Request)
		def mockLog = Mock(EthLog)

		web3j.ethGetLogs(_ as EthFilter) >> mockRequest
		mockRequest.send() >> mockLog
		mockLog.getLogs() >> resultList
	}

	/**
	 * Clear all contents from the test S3 bucket
	 */
	protected void clearS3Bucket() {
		try {
			String continuationToken = null
			boolean hasMoreObjects = true

			while (hasMoreObjects) {
				def listRequest = ListObjectsV2Request.builder()
						.bucket(TEST_BUCKET)
						.continuationToken(continuationToken)
						.build()

				def listResponse = s3Client.listObjectsV2(listRequest as ListObjectsV2Request)

				listResponse.contents().each { obj ->
					s3Client.deleteObject(DeleteObjectRequest.builder()
							.bucket(TEST_BUCKET)
							.key(obj.key())
							.build() as DeleteObjectRequest)
				}

				continuationToken = listResponse.nextContinuationToken()
				hasMoreObjects = (continuationToken != null)
			}

			def finalCheck = s3Client.listObjectsV2 {
				it.bucket(TEST_BUCKET)
			}
			if (finalCheck.contents().isEmpty()) {
				println("S3 bucket ${TEST_BUCKET} successfully cleared")
			} else {
				println("Warning: S3 bucket ${TEST_BUCKET} still contains ${finalCheck.contents().size()} objects")
			}
		} catch (Exception e) {
			println("Error clearing S3 bucket: ${e.message}")
			e.printStackTrace()
		}
	}

	/**
	 * Clear all DynamoDB tables used in tests
	 */
	protected void clearDynamoDBTables() {
		try {
			// Clear events table
			clearDynamoDBTable(EVENTS_TABLE, ["transactionHash", "logIndex"])

			// Clear block height table
			clearDynamoDBTable(BLOCK_HEIGHT_TABLE, ["id"])
		} catch (Exception e) {
			println("Error clearing DynamoDB tables: ${e.message}")
			e.printStackTrace()
		}
	}

	/**
	 * Clear a specific DynamoDB table
	 */
	protected void clearDynamoDBTable(String tableName, List<String> keyAttributes) {
		try {
			// Scan the table to get all items
			def scanRequest = ScanRequest.builder()
					.tableName(tableName)
					.build()

			def scanResponse = dynamoDbClient.scan(scanRequest as ScanRequest)

			// Delete each item
			scanResponse.items().each { item ->
				def keyMap = [:]
				keyAttributes.each { keyAttr ->
					if (item.containsKey(keyAttr)) {
						keyMap[keyAttr] = item[keyAttr]
					}
				}

				if (!keyMap.isEmpty()) {
					def deleteRequest = DeleteItemRequest.builder()
							.tableName(tableName)
							.key(keyMap as Map<String, AttributeValue>)
							.build()
					dynamoDbClient.deleteItem(deleteRequest as DeleteItemRequest)
				}
			}
		} catch (Exception e) {
			println("Error clearing DynamoDB table ${tableName}: ${e.message}")
		}
	}
}
