package adhoc.event_monitoring

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import ch.qos.logback.classic.Logger
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptor
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import io.reactivex.BackpressureStrategy
import io.reactivex.Flowable
import io.reactivex.processors.PublishProcessor
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean
import org.web3j.protocol.core.Request
import org.web3j.protocol.core.methods.response.EthBlock
import org.web3j.protocol.core.methods.response.Log
import org.web3j.protocol.websocket.events.NewHeadsNotification
import org.web3j.protocol.websocket.events.NotificationParams
import spock.lang.Shared

import java.math.BigInteger
import java.time.Instant
import java.util.Optional
import java.util.concurrent.CompletableFuture
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger

@SpringBootTest(
        classes = [BcmonitoringApplication.class],
        webEnvironment = SpringBootTest.WebEnvironment.NONE
)
class EventMonitoringITSpec extends BaseAdhocITSpec {

    @Shared
    def abiParserLogger = LoggerFactory.getLogger(AbiParser.class) as Logger

    @Autowired
    ApplicationContext applicationContext

    @MockitoSpyBean
    Web3jConfig web3jConfig

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("local-stack.end-point", { "http://localhost:" + AdhocHelper.getLocalStackPort() })
        registry.add("eagerStart", { "false" })
        registry.add("aws.dynamodb.table-prefix", { "" })
    }

    def setupSpec() {
        setupSpecCommon()
    }

    def cleanupSpec() {
        cleanupSpecCommon()
    }

    def setup() {
        setupCommon()
        // Upload real ABI files to
        AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", [
                "Token",
                "Account",
                "Provider"
        ])
        // Start log appender to capture logs
        abiParserLogger.addAppender(logAppender)
    }

    @Override
    Web3jConfig getWeb3jConfig() {
        return web3jConfig
    }

    def cleanup() {
        cleanupCommon()
    }



    /**
     * Test different approaches to mock notification
     */
    def "Should test different mock approaches"() {
        given: "Different mock approaches"
        System.out.println("=== Testing Different Mock Approaches ===")

        when: "Testing Approach 1: Stub instead of Mock"
        def notification1 = Stub(NewHeadsNotification)
        notification1.getParams() >> [getResult: { -> [getNumber: { -> "0x3e8" }] }]
        def result1 = null
        try {
            result1 = notification1.getParams().getResult().getNumber()
            System.out.println("Approach 1 (Stub): SUCCESS - ${result1}")
        } catch (Exception e) {
            System.out.println("Approach 1 (Stub): FAILED - ${e.class.simpleName}: ${e.message}")
        }

        and: "Testing Approach 2: Mock with proper objects"
        def notification2 = Mock(NewHeadsNotification)
        def params2 = Stub(NotificationParams)
        def result2Object = [getNumber: { -> "0x3e8" }]
        params2.getResult() >> result2Object
        notification2.getParams() >> params2
        def result2 = null
        try {
            result2 = notification2.getParams().getResult().getNumber()
            System.out.println("Approach 2 (Mock+Stub): SUCCESS - ${result2}")
        } catch (Exception e) {
            System.out.println("Approach 2 (Mock+Stub): FAILED - ${e.class.simpleName}: ${e.message}")
        }

        then: "At least one approach should work"
        result1 == "0x3e8" || result2 == "0x3e8"
        System.out.println("=== Mock Test Completed ===")
    }

    /**
     * Should detects and processes events from new blockchain blocks
     * Verifies service correctly detects and processes events
     * Expected: Events extracted, parsed, and saved to DynamoDB
     */
    def "Should detects and processes events from new blockchain blocks"() {
        given: "An empty DynamoDB BlockHeight and all dependencies available"

        // Create empty blocks (blocks that exist but contain no events)
        def emptyBlocks = createMockEmptyBlocks()
        setUpEventStream(emptyBlocks)
        setUpPendingEvent(Collections.emptyList())

        when: "The service starts"
        def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

        scheduler.schedule({
            AdhocHelper.stopBCMonitoring()
        }, 15, TimeUnit.SECONDS)
        commandLineRunner.run("-f")

        then: "No exceptions are thrown"
        noExceptionThrown()

        and: "Service starts and processes ABI files successfully"
        def messages = logAppender.list*.formattedMessage
        assert messages.any { it.contains("Started bc monitoring") }
        assert messages.any { it.contains("Monitoring events...") }

        and: "Service processes blocks even if they contain no events"
        // The service should process the empty blocks without errors
        println("Log messages:")
        messages.each { msg -> println("  - ${msg}") }

        and: "No events are saved to DynamoDB since blocks are empty"
        def eventsInDb = scanEventsTable()
        println("Events found in DynamoDB: ${eventsInDb?.size() ?: 'null'}")
        // Should be empty since we're testing with empty blocks
        assert eventsInDb == null || eventsInDb.isEmpty()
    }

    /**
     * Helper method to create mock empty blocks (blocks that exist but contain no events)
     */
    private List<NewHeadsNotification> createMockEmptyBlocks() {
        def notifications = []

        // Create mock notification for block 1000 with empty transactions
        def notification1 = Mock(NewHeadsNotification)
        notification1.getParams() >> [getResult: { -> [getNumber: { -> "0x3e8" }] }] // 1000 in hex
        notifications.add(notification1)

        // Create mock notification for block 1001 with empty transactions
        def notification2 = Mock(NewHeadsNotification)
        notification2.getParams() >> [getResult: { -> [getNumber: { -> "0x3e9" }] }] // 1001 in hex
        notifications.add(notification2)

        return notifications
    }
}
